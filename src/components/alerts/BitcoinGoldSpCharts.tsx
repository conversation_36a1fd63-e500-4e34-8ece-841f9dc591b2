import React from 'react'
import { HeroSection<PERSON><PERSON> } from '../home/<USER>'
import FullscreenWrapper from '../comman/FullscreenWrapper'
import { FullscreenProvider } from '../comman/FullscreenProvider'
import FullscreenButton from '../comman/FullscreenButton'

export default function BitcoinGoldSpCharts() {
  return (
    <FullscreenProvider>
      <div className="flex justify-between items-center">
        <h1 className="text-large font-semibold">Bitcoin vs Gold vs S&P 500</h1>
        <FullscreenButton />
      </div>
      <FullscreenWrapper title="Bitcoin vs Gold vs S&P 500">
        <div className="w-full space-y-8 py-6">
          <HeroSectionChart/>
        </div>
      </FullscreenWrapper>
    </FullscreenProvider>
  )
}