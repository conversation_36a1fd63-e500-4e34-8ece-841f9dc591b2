"use client";

import React, { useMemo } from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { useCoinglassData } from '@/lib/state';
import Loader from '../comman/Loader';
import FullscreenWrapper from '../comman/FullscreenWrapper';
import { FullscreenProvider } from '../comman/FullscreenProvider';
import FullscreenButton from '../comman/FullscreenButton';

const ExchangeVolumeHistoryChart: React.FC = () => {
  const { data: volumeData, isLoading, error } = useCoinglassData('exchange-volume-history');

  const { chartData } = useMemo(() => {
    if (!volumeData || !volumeData.time_list || !Array.isArray(volumeData.time_list)) {
      return { chartData: [], exchangeKeys: [] };
    }

    const timeList = volumeData.time_list;

    // Find volume data arrays (exclude time_list and other non-array fields)
    const volumeKeys = Object.keys(volumeData).filter(
      key => key !== 'time_list' && Array.isArray(volumeData[key])
    );

    // Transform the data for the chart
    const transformedData = timeList.map((timestamp: number, index: number) => {
      const date = new Date(timestamp);
      const dataPoint: any = {
        time: Math.floor(timestamp / 1000),
        date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        timestamp: timestamp,
      };

      // Add volume data for each exchange
      volumeKeys.forEach(exchangeKey => {
        const volumeArray = volumeData[exchangeKey] || [];
        dataPoint[exchangeKey] = volumeArray[index] || 0;
      });

      // Calculate total volume across all exchanges
      const totalVolume = volumeKeys.reduce((sum, key) => {
        const volumeArray = volumeData[key] || [];
        return sum + (volumeArray[index] || 0);
      }, 0);

      dataPoint.totalVolume = totalVolume;

      return dataPoint;
    });

    return { chartData: transformedData, exchangeKeys: volumeKeys };
  }, [volumeData]);

  const formatVolumeValue = (value: number) => {
    if (value >= 1e9) {
      return `$${(value / 1e9).toFixed(2)}B`;
    } else if (value >= 1e6) {
      return `$${(value / 1e6).toFixed(2)}M`;
    } else if (value >= 1e3) {
      return `$${(value / 1e3).toFixed(2)}K`;
    }
    return `$${value.toFixed(2)}`;
  };

  const formatTooltipValue = (value: number, name: string) => {
    const displayName = name === 'totalVolume' ? 'Total Volume' : name;
    return [formatVolumeValue(value), displayName];
  };

  const formatXAxisTick = (tickItem: any) => {
    const date = new Date(tickItem * 1000);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    });
  };

  if (isLoading) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-center" style={{ height: 500 }}>
          <Loader />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-center flex-col space-y-4" style={{ height: 500 }}>
          <p className="text-red-400 text-lg">Error loading exchange volume data</p>
          <p className="text-gray-400 text-sm text-center max-w-md">
            {error.message || 'Failed to fetch exchange volume history data'}
          </p>
        </div>
      </div>
    );
  }

  if (!chartData || chartData.length === 0) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-center flex-col space-y-4" style={{ height: 500 }}>
          <p className="text-white text-lg">Exchange Volume Data Coming Soon</p>
          <p className="text-gray-400 text-sm text-center max-w-md">
            The Coinglass API integration is ready. Backend endpoints are being deployed to fetch exchange volume history data.
          </p>
        </div>
      </div>
    );
  }

  return (
    <FullscreenProvider>
      <div className="flex justify-between items-center">
        <h1 className="text-large font-semibold">Exchange Volume History</h1>
        <FullscreenButton />
      </div>
      <FullscreenWrapper title="Exchange Volume History">
        <div className="w-full space-y-8">
        <div className="space-y-8">
          <div className="w-full space-y-4">
            <div className="w-full">
              <div style={{ width: "100%", height: 500 }}>
                <ResponsiveContainer width="100%" height={500}>
                  <LineChart
                    data={chartData}
                  >
                    <CartesianGrid stroke="#5d5e5f" strokeDasharray="6 6" />
                    <XAxis
                      dataKey="time"
                      tick={{ fill: "#fff", fontSize: 10 }}
                      tickFormatter={formatXAxisTick}
                      axisLine={false}
                      tickLine={false}
                      minTickGap={40}
                    />
                    <YAxis
                      orientation="right"
                      tick={{ fill: "#fff", fontSize: 10 }}
                      tickFormatter={(value) => formatVolumeValue(value)}
                      axisLine={false}
                      tickLine={false}
                      domain={['dataMin', 'dataMax']}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: "#1f2937",
                        border: "1px solid #374151",
                        borderRadius: "8px",
                        color: "#fff",
                      }}
                      labelStyle={{ color: "#9ca3af" }}
                      formatter={formatTooltipValue}
                      labelFormatter={(label) => {
                        const date = new Date(label * 1000);
                        return date.toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        });
                      }}
                    />
                    <Line
                      type="monotone"
                      dataKey="totalVolume"
                      stroke="#bbd955"
                      strokeWidth={1}
                      dot={false}
                      // activeDot={{ r: 4, fill: "#bbd955" }}
                      name="Total Volume"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        </div>
        </div>
      </FullscreenWrapper>
    </FullscreenProvider>
  );
};

export default ExchangeVolumeHistoryChart;
